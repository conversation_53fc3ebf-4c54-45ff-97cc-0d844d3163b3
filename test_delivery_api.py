#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试外卖配送价格API的脚本
支持测试门店发单和点对点发单两种模式
"""

import json
import requests
import os
from typing import Dict, Any

def test_delivery_price_api(delivery_mode: str = "store"):
    """测试获取配送价格的API

    Args:
        delivery_mode: 配送模式，"store" 或 "point_to_point"
    """

    # API端点
    url = "http://localhost:8000/api/v1/wechat_mini_app/delivery-order/create"
    
    # 测试数据
    test_data = {
        "order_type": "delivery",
        "items": [
            {
                "dish_id": 1,
                "name": "素食套餐A",
                "price": 25.00,
                "quantity": 2
            },
            {
                "dish_id": 2,
                "name": "素食套餐B", 
                "price": 30.00,
                "quantity": 1
            }
        ],
        "total_amount": 80.00,
        "delivery_address": {
            "name": "张三",
            "phone": "13800138000",
            "detail": "广州市天河区珠江新城某某大厦1001室",
            "province": "广东省",
            "city": "广州市",
            "district": "天河区",
            "latitude": 23.120000,  # 收货点纬度
            "longitude": 113.320000  # 收货点经度
        },
        "delivery_time": {
            "type": "immediate",
            "value": ""
        }
    }
    
    # 请求头
    headers = {
        "Content-Type": "application/json",
        "token": "your-test-token-here"  # 需要替换为有效的token
    }
    
    try:
        print(f"发送测试请求 (配送模式: {delivery_mode})...")
        print(f"URL: {url}")
        print(f"数据: {json.dumps(test_data, ensure_ascii=False, indent=2)}")

        response = requests.post(url, json=test_data, headers=headers)

        print(f"\n响应状态码: {response.status_code}")
        print(f"响应内容: {json.dumps(response.json(), ensure_ascii=False, indent=2)}")

        if response.status_code == 200:
            result = response.json()
            if result.get("code") == 200:
                data = result.get("data", {})
                print(f"\n✅ 获取配送价格成功! (配送模式: {delivery_mode})")
                print(f"配送费: {data.get('delivery_fee')}元")
                print(f"预计送达时间: {data.get('predict_delivery_minutes')}分钟")
                print(f"配送距离: {data.get('distance')}米")
                print(f"服务介绍: {data.get('slogan')}")
            else:
                print(f"\n❌ API返回错误: {result.get('message')}")
        else:
            print(f"\n❌ HTTP请求失败: {response.status_code}")

    except Exception as e:
        print(f"\n💥 测试过程中发生异常: {str(e)}")

def test_both_delivery_modes():
    """测试两种配送模式"""
    print("=" * 60)
    print("测试门店发单模式")
    print("=" * 60)

    # 设置环境变量为门店模式
    os.environ["FENGNIAO_DELIVERY_MODE"] = "store"
    test_delivery_price_api("store")

    print("\n" + "=" * 60)
    print("测试点对点发单模式")
    print("=" * 60)

    # 设置环境变量为点对点模式
    os.environ["FENGNIAO_DELIVERY_MODE"] = "point_to_point"
    test_delivery_price_api("point_to_point")

def print_expected_response():
    """打印预期的API返回格式"""
    expected_response = {
        "code": 200,
        "message": "success",
        "data": {
            "delivery_fee": 11.70,
            "delivery_fee_cent": 1170,
            "predict_delivery_minutes": 64,
            "distance": 4447,
            "service_goods_id": 3008,
            "base_goods_id": 30024,
            "slogan": "24小时服务，灵活配送准点达",
            "t_index_id": "b69d90c2-b7f0-4f0b-a62c-4ad64aa50835"
        }
    }
    
    print("预期的API返回格式:")
    print(json.dumps(expected_response, ensure_ascii=False, indent=2))

if __name__ == "__main__":
    print("外卖配送价格API测试脚本")
    print("支持门店发单和点对点发单两种模式")
    print("=" * 60)

    print("\n1. 预期返回格式:")
    print_expected_response()

    print("\n2. 开始测试API (两种配送模式):")
    test_both_delivery_modes()

    print("\n" + "=" * 60)
    print("测试完成!")
    print("注意: 根据蜂鸟文档，预下单接口仅支持门店发单模式")
    print("如果点对点模式测试失败，这是正常现象")
