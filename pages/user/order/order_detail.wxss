/* pages/user/order/order_detail.wxss */
@import "../../../pages/user/user.wxss";

/* 到店指引样式 */
.store-guide-section {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  margin-bottom: 20rpx;
  border-radius: 12rpx;
  box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.3);
  overflow: hidden;
}

.store-guide-section .section-title {
  background: rgba(255, 255, 255, 0.1);
  padding: 24rpx;
  font-size: 28rpx;
  font-weight: 600;
  color: #fff;
  border-bottom: 1rpx solid rgba(255, 255, 255, 0.1);
  display: flex;
  align-items: center;
}

.title-icon {
  margin-right: 12rpx;
  font-size: 32rpx;
}

.store-guide-content {
  padding: 24rpx;
}

.store-info-row {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20rpx;
}

.store-basic-info {
  flex: 1;
  margin-right: 20rpx;
}

.store-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #fff;
  margin-bottom: 8rpx;
}

.store-address {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.4;
}

.store-distance {
  display: flex;
  flex-direction: column;
  align-items: center;
  background: rgba(255, 255, 255, 0.15);
  padding: 16rpx 20rpx;
  border-radius: 8rpx;
  min-width: 100rpx;
}

.distance-text {
  font-size: 28rpx;
  font-weight: 600;
  color: #fff;
  line-height: 1;
}

.distance-unit {
  font-size: 20rpx;
  color: rgba(255, 255, 255, 0.7);
  margin-top: 4rpx;
}

.pickup-time-row {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
  padding: 16rpx 20rpx;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8rpx;
}

.pickup-time-label {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
  margin-right: 12rpx;
}

.pickup-time-value {
  font-size: 24rpx;
  color: #fff;
  font-weight: 500;
}

.guide-actions {
  display: flex;
  gap: 16rpx;
}

.guide-btn {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20rpx 24rpx;
  border-radius: 8rpx;
  font-size: 26rpx;
  font-weight: 500;
  border: none;
  transition: all 0.3s ease;
}

.btn-icon {
  margin-right: 8rpx;
  font-size: 28rpx;
}

.location-btn {
  background: rgba(255, 255, 255, 0.2);
  color: #fff;
  border: 1rpx solid rgba(255, 255, 255, 0.3);
}

.location-btn:active {
  background: rgba(255, 255, 255, 0.3);
}

.location-btn[disabled] {
  opacity: 0.6;
}

.navigate-btn {
  background: #fff;
  color: #667eea;
}

.navigate-btn:active {
  background: #f0f0f0;
}

.order-detail {
  padding: 0;
}

.detail-section {
  background: #fff;
  margin-bottom: 20rpx;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.section-title {
  background: #f8f8f8;
  padding: 24rpx;
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  border-bottom: 1rpx solid #e0e0e0;
}

.detail-row {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 20rpx 24rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.detail-row:last-child {
  border-bottom: none;
}

.detail-label {
  font-size: 26rpx;
  color: #666;
  min-width: 160rpx;
  flex-shrink: 0;
}

.detail-value {
  font-size: 26rpx;
  color: #333;
  text-align: right;
  flex: 1;
  word-break: break-all;
}

.detail-value.status.pending {
  color: #ff9500;
}

.detail-value.status.paid {
  color: #34c759;
}

.detail-value.status.shipped {
  color: #007aff;
}

.detail-value.status.delivered {
  color: #30d158;
}

.detail-value.status.completed {
  color: #34c759;
}

.detail-value.status.cancelled {
  color: #ff3b30;
}

.detail-value.status.refunded {
  color: #ff9500;
}

.detail-value.amount {
  font-weight: 600;
  color: #333;
}

.detail-value.amount.payable {
  color: #ff9500;
}

.detail-value.amount.paid {
  color: #34c759;
}

.detail-value.amount.discount {
  color: #ff3b30;
}

/* 商品列表样式 */
.item-list {
  padding: 0;
}

.item-row {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 24rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.item-row:last-child {
  border-bottom: none;
}

.item-info {
  flex: 1;
  margin-right: 20rpx;
}

.item-name {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}

.item-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4rpx;
}

.item-price,
.item-quantity,
.item-final-price,
.item-remark {
  font-size: 24rpx;
  color: #666;
}

.item-final-price {
  color: #ff9500;
}

.item-amount {
  text-align: right;
  min-width: 120rpx;
}

.subtotal {
  font-size: 26rpx;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 4rpx;
}

.payable-amount {
  font-size: 22rpx;
  color: #34c759;
  display: block;
}

/* 优惠券列表样式 */
.coupon-list {
  padding: 0;
}

.coupon-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 24rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.coupon-row:last-child {
  border-bottom: none;
}

.coupon-info {
  flex: 1;
}

.coupon-name {
  font-size: 26rpx;
  color: #333;
  margin-bottom: 4rpx;
}

.coupon-time {
  font-size: 22rpx;
  color: #999;
}

.coupon-discount {
  font-size: 26rpx;
  font-weight: 600;
  color: #ff3b30;
}

/* 操作按钮区域 */
.action-section {
  background: #fff;
  padding: 24rpx;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  text-align: center;
}

.cancel-btn {
  background: #ff3b30;
  color: #fff;
  border: none;
  border-radius: 8rpx;
  padding: 20rpx 40rpx;
  font-size: 28rpx;
  font-weight: 600;
  min-width: 200rpx;
}

.cancel-btn:active {
  background: #d70015;
}

/* 加载状态样式 */
.loading-state {
  text-align: center;
  padding: 120rpx 40rpx;
}

.loading-icon {
  font-size: 120rpx;
  margin-bottom: 24rpx;
  opacity: 0.6;
  animation: rotate 2s linear infinite;
}

.loading-text {
  font-size: 28rpx;
  color: #999;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 底部按钮容器 */
.bottom-button-container {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  padding: 20rpx 40rpx;
  border-top: 1rpx solid #e0e0e0;
  box-shadow: 0 -2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.back-button {
  width: 100%;
  background: #007aff;
  color: #fff;
  border: none;
  border-radius: 8rpx;
  padding: 24rpx;
  font-size: 32rpx;
  font-weight: 600;
}

.back-button:active {
  background: #0056cc;
}

/* 为底部按钮留出空间 */
.readlog-container {
  padding-bottom: 120rpx;
}
