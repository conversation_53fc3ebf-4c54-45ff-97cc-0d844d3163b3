// pages/user/order/order_detail.js
import { 
  getOrderDetail, 
  cancelOrder, 
  formatOrderStatus, 
  formatPaymentStatus, 
  formatOrderType,
  formatPaymentMethod,
  formatDateTime,
  canCancelOrder 
} from '../../../service/order';
import { checkLogin } from '../../../service/user';

const app = getApp();

Page({
  data: {
    isLogin: false,
    order: null,
    loading: true,
    orderId: null,
    // 店铺信息
    storeInfo: {
      name: '乙禾素食餐厅',
      address: '广州市黄埔区黄埔大道东976号港航中心二期B座1601房',
      latitude: 23.0959878,
      longitude: 113.431943
    },
    // 位置相关
    currentDistance: null,
    locationLoading: false,
    pickupTimeInfo: '营业时间：09:00-21:00'
  },

  onLoad: function(options) {
    wx.setNavigationBarTitle({
      title: '订单详情'
    });
    
    if (options.id) {
      this.setData({
        orderId: options.id
      });
      this.checkLoginAndLoadData();
    } else {
      wx.showToast({
        title: '订单ID不能为空',
        icon: 'none'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    }
  },

  async onShow() {
    try {
      const isLoggedIn = await checkLogin();
      if (!isLoggedIn) {
        const app = getApp();
        if (app && app.monitor_token) {
          await app.monitor_token();
        } else {
          wx.navigateTo({
            url: '/pages/index/index'
          });
        }
      }
    } catch (error) {
      console.error('检查登录状态失败', error);
    }
    this.checkLoginStatus();
  },

  checkLoginStatus: function() {
    const token = wx.getStorageSync('token') || '';
    
    if (token && app.globalData && app.globalData.userInfo) {
      this.setData({
        isLogin: true
      });
    } else {
      this.setData({
        isLogin: !!token
      });
    }
  },

  checkLoginAndLoadData: function() {
    this.checkLoginStatus();
    if (this.data.isLogin && this.data.orderId) {
      this.loadOrderDetail();
    }
  },

  // 加载订单详情
  loadOrderDetail: function() {
    this.setData({ loading: true });

    getOrderDetail(this.data.orderId)
      .then(res => {
        console.log('订单详情响应:', res);
        
        if (res.code === 200 && res.data) {
          const order = res.data;
          
          // 格式化订单数据
          const formattedOrder = {
            ...order,
            statusText: formatOrderStatus(order.status),
            paymentStatusText: formatPaymentStatus(order.payment_status),
            typeText: formatOrderType(order.type),
            paymentMethodText: formatPaymentMethod(order.payment_method),
            createdAtText: formatDateTime(order.created_at),
            updatedAtText: formatDateTime(order.updated_at),
            paymentTimeText: formatDateTime(order.payment_time),
            canCancel: canCancelOrder(order),
            // 格式化优惠券使用时间
            coupon_discounts: order.coupon_discounts ? order.coupon_discounts.map(coupon => ({
              ...coupon,
              used_at: formatDateTime(coupon.used_at)
            })) : []
          };

          // 设置自提时间信息
          let pickupTimeInfo = '营业时间：09:00-21:00';
          if (formattedOrder.status === 'paid' || formattedOrder.status === 'completed') {
            pickupTimeInfo = '可随时自提（营业时间内）';
          } else if (formattedOrder.status === 'pending') {
            pickupTimeInfo = '支付后可自提';
          }

          this.setData({
            order: formattedOrder,
            loading: false,
            pickupTimeInfo: pickupTimeInfo
          });

          // 如果是自提订单，尝试自动获取距离
          if (formattedOrder.type === 'order') {
            this.tryAutoGetDistance();
          }
        } else {
          throw new Error(res.message || '获取订单详情失败');
        }
      })
      .catch(error => {
        console.error('获取订单详情失败:', error);
        this.setData({
          loading: false
        });
        wx.showToast({
          title: error.message || '获取订单详情失败',
          icon: 'none'
        });
      });
  },

  // 取消订单
  cancelOrder: function() {
    const order = this.data.order;
    if (!order) return;
    
    wx.showModal({
      title: '确认取消',
      content: `确定要取消订单 ${order.order_no} 吗？`,
      success: (res) => {
        if (res.confirm) {
          this.performCancelOrder();
        }
      }
    });
  },

  // 执行取消订单操作
  performCancelOrder: function() {
    wx.showLoading({
      title: '取消中...'
    });

    cancelOrder(this.data.orderId)
      .then(res => {
        wx.hideLoading();
        console.log('取消订单响应:', res);
        
        if (res.code === 200) {
          wx.showToast({
            title: '订单取消成功',
            icon: 'success'
          });
          
          // 重新加载订单详情
          this.loadOrderDetail();
        } else {
          throw new Error(res.message || '取消订单失败');
        }
      })
      .catch(error => {
        wx.hideLoading();
        console.error('取消订单失败:', error);
        wx.showToast({
          title: error.message || '取消订单失败',
          icon: 'none'
        });
      });
  },

  // 登录按钮点击事件
  handleLogin: function() {
    wx.navigateTo({
      url: '/pages/phoneAuth/phoneAuth'
    });
  },

  // 尝试自动获取距离（静默，不显示错误）
  tryAutoGetDistance: function() {
    wx.getSetting({
      success: (res) => {
        if (res.authSetting['scope.userLocation']) {
          // 已授权，静默获取位置
          wx.getLocation({
            type: 'gcj02',
            success: (locationRes) => {
              const distance = this.calculateDistance(
                locationRes.latitude,
                locationRes.longitude,
                this.data.storeInfo.latitude,
                this.data.storeInfo.longitude
              );
              this.setData({
                currentDistance: distance.toFixed(1)
              });
            },
            fail: () => {
              // 静默失败，不显示错误
            }
          });
        }
      }
    });
  },

  // 获取当前位置并计算距离
  getCurrentLocation: function() {
    this.setData({ locationLoading: true });

    wx.getLocation({
      type: 'gcj02',
      success: (res) => {
        console.log('获取到当前位置:', res);
        const distance = this.calculateDistance(
          res.latitude,
          res.longitude,
          this.data.storeInfo.latitude,
          this.data.storeInfo.longitude
        );

        this.setData({
          currentDistance: distance.toFixed(1),
          locationLoading: false
        });
      },
      fail: (err) => {
        console.error('获取位置失败:', err);
        this.setData({ locationLoading: false });

        wx.showModal({
          title: '位置权限',
          content: '需要获取您的位置信息来计算距离，请在设置中开启位置权限',
          showCancel: true,
          confirmText: '去设置',
          success: (modalRes) => {
            if (modalRes.confirm) {
              wx.openSetting();
            }
          }
        });
      }
    });
  },

  // 计算两点间距离（单位：公里）
  calculateDistance: function(lat1, lng1, lat2, lng2) {
    const radLat1 = lat1 * Math.PI / 180.0;
    const radLat2 = lat2 * Math.PI / 180.0;
    const a = radLat1 - radLat2;
    const b = lng1 * Math.PI / 180.0 - lng2 * Math.PI / 180.0;
    const s = 2 * Math.asin(Math.sqrt(Math.pow(Math.sin(a / 2), 2) +
      Math.cos(radLat1) * Math.cos(radLat2) * Math.pow(Math.sin(b / 2), 2)));
    return s * 6378.137; // 地球半径
  },

  // 导航到店铺
  navigateToStore: function() {
    const storeInfo = this.data.storeInfo;

    wx.openLocation({
      latitude: storeInfo.latitude,
      longitude: storeInfo.longitude,
      name: storeInfo.name,
      address: storeInfo.address,
      scale: 18,
      success: () => {
        console.log('打开地图导航成功');
      },
      fail: (err) => {
        console.error('打开地图导航失败:', err);
        wx.showToast({
          title: '打开地图失败',
          icon: 'none'
        });
      }
    });
  },

  // 返回按钮
  navigateBack: function() {
    wx.navigateBack({
      delta: 1
    });
  }
});
