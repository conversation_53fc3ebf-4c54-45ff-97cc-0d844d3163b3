/**
 * 外卖订单提交验证脚本
 * 用于验证修改后的订单提交功能是否正确
 */

// 模拟前端数据结构
const mockData = {
  cartItems: [
    {
      id: 1,
      name: '素食套餐A',
      price: 25.00,
      count: 2
    },
    {
      id: 2,
      name: '素食套餐B', 
      price: 30.00,
      count: 1
    }
  ],
  cartTotal: {
    count: 3,
    price: 80.00
  },
  deliveryAddress: {
    id: '1234567890',
    name: '张三',
    phone: '13800138000',
    detail: '广州市天河区珠江新城某某大厦1001室',
    province: '广东省',
    city: '广州市',
    district: '天河区',
    latitude: 23.120000,
    longitude: 113.320000,
    label: '家',
    isDefault: true
  },
  selectedTimeSlot: {
    id: '1',
    label: '09:00-12:00',
    value: '09:00-12:00'
  },
  deliveryFee: 11.70,
  deliveryFeeData: {
    delivery_fee: 11.70,
    delivery_fee_cent: 1170,
    predict_delivery_minutes: 64,
    distance: 4447,
    service_goods_id: 3008,
    base_goods_id: 30024,
    slogan: '24小时服务，灵活配送准点达',
    t_index_id: 'b69d90c2-b7f0-4f0b-a62c-4ad64aa50835'
  },
  selectedCouponIds: [14, 17],
  couponDiscount: 5.00,
  finalAmount: 86.70 // 80.00 + 11.70 - 5.00
};

/**
 * 验证订单数据格式
 */
function validateOrderData(data) {
  console.log('=== 开始验证订单数据 ===');
  
  // 生成订单项
  const orderItems = data.cartItems.map(item => ({
    dish_id: item.id,
    name: item.name,
    price: item.price,
    quantity: item.count
  }));

  // 构建订单数据
  const orderData = {
    order_type: 'delivery',
    items: orderItems,
    total_amount: data.cartTotal.price, // 商品总金额，不包含配送费
    delivery_address: data.deliveryAddress,
    delivery_time: data.selectedTimeSlot,
    delivery_fee: data.deliveryFee,
    // 蜂鸟相关必需字段
    service_goods_id: data.deliveryFeeData.service_goods_id,
    base_goods_id: data.deliveryFeeData.base_goods_id,
    t_index_id: data.deliveryFeeData.t_index_id
  };

  // 添加优惠券信息（如果有）
  if (data.selectedCouponIds && data.selectedCouponIds.length > 0) {
    orderData.coupon_id = data.selectedCouponIds.join(',');
    orderData.coupon_discount = data.couponDiscount;
  }

  console.log('生成的订单数据:', JSON.stringify(orderData, null, 2));

  // 验证必填字段
  const requiredFields = [
    'order_type', 'items', 'total_amount', 'delivery_address', 
    'delivery_fee', 'service_goods_id', 'base_goods_id', 't_index_id'
  ];

  const missingFields = [];
  requiredFields.forEach(field => {
    if (!(field in orderData) || orderData[field] === null || orderData[field] === undefined) {
      missingFields.push(field);
    }
  });

  if (missingFields.length > 0) {
    console.error('❌ 缺少必填字段:', missingFields);
    return false;
  }

  // 验证订单类型
  if (orderData.order_type !== 'delivery') {
    console.error('❌ 订单类型错误:', orderData.order_type);
    return false;
  }

  // 验证商品列表
  if (!Array.isArray(orderData.items) || orderData.items.length === 0) {
    console.error('❌ 商品列表为空或格式错误');
    return false;
  }

  // 验证商品项必填字段
  for (const item of orderData.items) {
    const itemRequiredFields = ['dish_id', 'quantity'];
    for (const field of itemRequiredFields) {
      if (!(field in item) || item[field] === null || item[field] === undefined) {
        console.error(`❌ 商品项缺少必填字段: ${field}`, item);
        return false;
      }
    }
    
    if (typeof item.quantity !== 'number' || item.quantity <= 0) {
      console.error('❌ 商品数量必须为正数:', item);
      return false;
    }
  }

  // 验证配送地址
  if (typeof orderData.delivery_address !== 'object') {
    console.error('❌ 配送地址格式错误');
    return false;
  }

  const addressRequiredFields = ['name', 'phone', 'detail'];
  for (const field of addressRequiredFields) {
    if (!(field in orderData.delivery_address) || !orderData.delivery_address[field]) {
      console.error(`❌ 配送地址缺少必填字段: ${field}`);
      return false;
    }
  }

  // 验证金额
  if (typeof orderData.total_amount !== 'number' || orderData.total_amount <= 0) {
    console.error('❌ 订单总金额必须为正数:', orderData.total_amount);
    return false;
  }

  if (typeof orderData.delivery_fee !== 'number' || orderData.delivery_fee < 0) {
    console.error('❌ 配送费用格式错误:', orderData.delivery_fee);
    return false;
  }

  // 验证蜂鸟字段
  const fengniaFields = ['service_goods_id', 'base_goods_id', 't_index_id'];
  for (const field of fengniaFields) {
    if (!orderData[field]) {
      console.error(`❌ 蜂鸟字段不能为空: ${field}`);
      return false;
    }
  }

  console.log('✅ 订单数据验证通过');
  return true;
}

/**
 * 验证配送费用数据
 */
function validateDeliveryFeeData(data) {
  console.log('\n=== 验证配送费用数据 ===');
  
  if (!data.deliveryFeeData) {
    console.error('❌ 缺少配送费用数据');
    return false;
  }

  const requiredFields = ['service_goods_id', 'base_goods_id', 't_index_id', 'delivery_fee'];
  const missingFields = [];
  
  requiredFields.forEach(field => {
    if (!(field in data.deliveryFeeData) || data.deliveryFeeData[field] === null) {
      missingFields.push(field);
    }
  });

  if (missingFields.length > 0) {
    console.error('❌ 配送费用数据缺少必填字段:', missingFields);
    return false;
  }

  console.log('✅ 配送费用数据验证通过');
  return true;
}

/**
 * 验证地址数据
 */
function validateAddressData(address) {
  console.log('\n=== 验证地址数据 ===');
  
  if (!address) {
    console.error('❌ 缺少配送地址');
    return false;
  }

  const requiredFields = ['name', 'phone', 'detail'];
  const missingFields = [];
  
  requiredFields.forEach(field => {
    if (!(field in address) || !address[field]) {
      missingFields.push(field);
    }
  });

  if (missingFields.length > 0) {
    console.error('❌ 地址数据缺少必填字段:', missingFields);
    return false;
  }

  // 检查坐标（建议有，但不是必须）
  if (!address.latitude || !address.longitude) {
    console.warn('⚠️  地址缺少坐标信息，可能影响配送服务');
  }

  console.log('✅ 地址数据验证通过');
  return true;
}

/**
 * 主验证函数
 */
function runValidation() {
  console.log('外卖订单提交数据验证');
  console.log('========================');
  
  let allValid = true;
  
  // 验证配送费用数据
  if (!validateDeliveryFeeData(mockData)) {
    allValid = false;
  }
  
  // 验证地址数据
  if (!validateAddressData(mockData.deliveryAddress)) {
    allValid = false;
  }
  
  // 验证订单数据
  if (!validateOrderData(mockData)) {
    allValid = false;
  }
  
  console.log('\n=== 验证结果 ===');
  if (allValid) {
    console.log('🎉 所有验证通过！订单数据格式正确，可以提交给后端。');
  } else {
    console.log('❌ 验证失败！请检查上述错误并修正。');
  }
  
  return allValid;
}

// 如果在Node.js环境中运行
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    validateOrderData,
    validateDeliveryFeeData,
    validateAddressData,
    runValidation,
    mockData
  };
}

// 如果在浏览器环境中运行
if (typeof window !== 'undefined') {
  window.deliveryOrderValidator = {
    validateOrderData,
    validateDeliveryFeeData,
    validateAddressData,
    runValidation,
    mockData
  };
}

// 直接运行验证
runValidation();
