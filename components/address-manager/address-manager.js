Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 当前选中的地址
    selectedAddress: {
      type: Object,
      value: null
    },
    // 占位符文本
    placeholder: {
      type: String,
      value: '请选择配送地址'
    },
    // 是否显示地址选择器
    visible: {
      type: Boolean,
      value: false
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    // 地址管理相关
    savedAddresses: [],
    showAddressList: false,
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 加载用户地址
     */
    loadUserAddress() {
      // 从本地存储获取保存的地址列表
      const savedAddresses = wx.getStorageSync('savedAddresses') || [];
      // 查找默认地址
      const defaultAddress = savedAddresses.find(addr => addr.isDefault) || savedAddresses[0] || null;

      this.setData({
        savedAddresses
      });

      // 如果没有选中地址且有默认地址，则设置默认地址
      if (!this.data.selectedAddress && defaultAddress) {
        this.setData({
          selectedAddress: defaultAddress
        });
        // 通知父组件地址变化
        this.triggerEvent('addresschange', defaultAddress);
      }
    },

    /**
     * 使用插件新增地址
     */
    addAddressWithPlugin() {
      try {
        wx.navigateTo({
          url: 'plugin://address-form/index',
          fail: (err) => {
            console.error('插件跳转失败:', err);
            wx.showToast({
              title: '地址插件暂时无法使用，请稍后重试',
              icon: 'none',
              duration: 2000
            });
          }
        });
      } catch (error) {
        console.error('插件调用异常:', error);
        wx.showToast({
          title: '地址插件暂时无法使用，请稍后重试',
          icon: 'none',
          duration: 2000
        });
      }
    },

    /**
     * 使用插件编辑地址
     */
    editAddressWithPlugin(e) {
      const addressId = e.currentTarget.dataset.id;
      const address = this.data.savedAddresses.find(addr => addr.id === addressId);

      if (address) {
        try {
          // 转换地址格式为插件需要的格式
          const editInfo = {
            receiver: address.name,
            phone: address.phone,
            area: [
              { name: address.province, id: '' },
              { name: address.city, id: '' },
              { name: address.district, id: '' }
            ],
            address: address.detail,
            label: address.label || '',
            default: address.isDefault
          };

          wx.navigateTo({
            url: `plugin://address-form/index?edit=${JSON.stringify(editInfo)}`,
            fail: (err) => {
              console.error('插件编辑跳转失败:', err);
              wx.showToast({
                title: '地址插件暂时无法使用，请稍后重试',
                icon: 'none',
                duration: 2000
              });
            }
          });
        } catch (error) {
          console.error('插件编辑调用异常:', error);
          wx.showToast({
            title: '地址插件暂时无法使用，请稍后重试',
            icon: 'none',
            duration: 2000
          });
        }
      }
    },

    /**
     * 保存插件返回的地址数据
     */
    savePluginAddress(data) {
      return new Promise((resolve, reject) => {
        try {
          console.log('保存插件地址数据:', data);

          // 验证必要字段
          if (!data.receiver || !data.phone || !data.address) {
            throw new Error('地址信息不完整');
          }

          // 转换插件数据格式为本地格式
          const newAddress = {
            id: Date.now().toString(),
            name: data.receiver,
            phone: data.phone,
            province: data.area && data.area[0] ? data.area[0].name : '广东省',
            city: data.area && data.area[1] ? data.area[1].name : '广州市',
            district: data.area && data.area[2] ? data.area[2].name : '黄埔区',
            detail: data.address,
            label: data.label || '',
            isDefault: data.default || false,
            latitude: data.latitude || null,
            longitude: data.longitude || null
          };

          // 如果插件没有提供经纬度，尝试通过地理编码获取
          if (!newAddress.latitude || !newAddress.longitude) {
            this.geocodeAddress(newAddress).then((coordinates) => {
              if (coordinates) {
                newAddress.latitude = coordinates.latitude;
                newAddress.longitude = coordinates.longitude;
              }
              this.finalizeSaveAddress(newAddress, resolve, reject);
            }).catch(() => {
              // 即使地理编码失败，也保存地址（经纬度为null）
              console.warn('地理编码失败，保存地址但经纬度为空');
              this.finalizeSaveAddress(newAddress, resolve, reject);
            });
          } else {
            this.finalizeSaveAddress(newAddress, resolve, reject);
          }
        } catch (error) {
          console.error('保存地址失败:', error);
          reject(error);
        }
      });
    },

    /**
     * 完成地址保存的最终步骤
     */
    finalizeSaveAddress(newAddress, resolve, reject) {
      try {
        let savedAddresses = [...this.data.savedAddresses];

        // 如果设置为默认地址，先取消其他地址的默认状态
        if (newAddress.isDefault) {
          savedAddresses = savedAddresses.map(addr => ({ ...addr, isDefault: false }));
        }

        // 如果是第一个地址，自动设为默认
        if (savedAddresses.length === 0) {
          newAddress.isDefault = true;
        }

        // 添加新地址
        savedAddresses.push(newAddress);

        this.setData({
          savedAddresses: savedAddresses
        });

        // 保存到本地存储
        this.saveAddressesToStorage();

        // 如果是默认地址或第一个地址，设置为当前选中地址
        if (newAddress.isDefault || savedAddresses.length === 1) {
          this.setData({
            selectedAddress: newAddress
          });
          // 通知父组件地址变化
          this.triggerEvent('addresschange', newAddress);
        }

        console.log('地址保存成功:', newAddress);
        resolve(newAddress);
      } catch (error) {
        console.error('完成地址保存失败:', error);
        reject(error);
      }
    },

    /**
     * 地理编码 - 根据地址获取经纬度
     */
    geocodeAddress(address) {
      return new Promise((resolve, reject) => {
        const fullAddress = `${address.province}${address.city}${address.district}${address.detail}`;
        const key = 'CHMBZ-6DYKL-JW6PE-EJPKN-4POB7-VYF23';

        console.log('开始地理编码:', fullAddress);

        wx.request({
          url: 'https://apis.map.qq.com/ws/geocoder/v1/',
          data: {
            address: fullAddress,
            key: key
          },
          success: (res) => {
            if (res.data.status === 0 && res.data.result && res.data.result.location) {
              const location = res.data.result.location;
              console.log('地理编码成功:', location);
              resolve({
                latitude: location.lat,
                longitude: location.lng
              });
            } else {
              console.error('地理编码失败:', res.data);
              reject(new Error('地理编码失败'));
            }
          },
          fail: (err) => {
            console.error('地理编码请求失败:', err);
            reject(err);
          }
        });
      });
    },

    /**
     * 保存地址到本地存储
     */
    saveAddressesToStorage() {
      wx.setStorageSync('savedAddresses', this.data.savedAddresses);
    },

    /**
     * 显示地址选择
     */
    showAddressSelector() {
      this.setData({
        showAddressList: true
      });
    },

    /**
     * 隐藏地址选择
     */
    hideAddressSelector() {
      this.setData({
        showAddressList: false
      });
    },

    /**
     * 选择地址
     */
    selectAddress(e) {
      const addressId = e.currentTarget.dataset.id;
      const selectedAddress = this.data.savedAddresses.find(addr => addr.id === addressId);

      if (selectedAddress) {
        this.setData({
          selectedAddress: selectedAddress,
          showAddressList: false
        });
        // 通知父组件地址变化
        this.triggerEvent('addresschange', selectedAddress);
      }
    },







    /**
     * 获取当前位置（保留原有方法作为备用）
     */
    getCurrentLocation() {
      wx.showLoading({ title: '获取位置中...' });

      // 先检查位置权限
      wx.getSetting({
        success: (res) => {
          if (res.authSetting['scope.userLocation']) {
            // 已授权，直接获取位置
            this.getLocationData();
          } else {
            // 未授权，请求授权
            wx.authorize({
              scope: 'scope.userLocation',
              success: () => {
                // 授权成功，获取位置
                this.getLocationData();
              },
              fail: () => {
                wx.hideLoading();
                wx.showModal({
                  title: '位置权限',
                  content: '需要获取您的位置信息来自动填充地址，请在设置中开启位置权限',
                  showCancel: true,
                  confirmText: '去设置',
                  success: (modalRes) => {
                    if (modalRes.confirm) {
                      wx.openSetting();
                    }
                  }
                });
              }
            });
          }
        },
        fail: () => {
          wx.hideLoading();
          wx.showToast({
            title: '获取权限信息失败',
            icon: 'none'
          });
        }
      });
    },









    /**
     * 删除地址
     */
    deleteAddress(e) {
      const addressId = e.currentTarget.dataset.id;
      const address = this.data.savedAddresses.find(addr => addr.id === addressId);

      if (!address) return;

      wx.showModal({
        title: '确认删除',
        content: `确定要删除地址"${address.name} ${address.detail}"吗？`,
        success: (res) => {
          if (res.confirm) {
            let newAddresses = this.data.savedAddresses.filter(addr => addr.id !== addressId);

            // 如果删除的是当前选中的地址，重新选择默认地址
            let newSelectedAddress = this.data.selectedAddress;
            if (this.data.selectedAddress && this.data.selectedAddress.id === addressId) {
              newSelectedAddress = newAddresses.find(addr => addr.isDefault) || newAddresses[0] || null;
            }

            this.setData({
              savedAddresses: newAddresses,
              selectedAddress: newSelectedAddress
            });

            // 保存到本地存储
            this.saveAddressesToStorage();

            // 通知父组件地址变化
            this.triggerEvent('addresschange', newSelectedAddress);

            wx.showToast({
              title: '地址删除成功',
              icon: 'success'
            });
          }
        }
      });
    },

    /**
     * 设置默认地址
     */
    setDefaultAddress(e) {
      const addressId = e.currentTarget.dataset.id;

      const newAddresses = this.data.savedAddresses.map(addr => ({
        ...addr,
        isDefault: addr.id === addressId
      }));

      const defaultAddress = newAddresses.find(addr => addr.id === addressId);

      this.setData({
        savedAddresses: newAddresses,
        selectedAddress: defaultAddress,
        showAddressList: false
      });

      // 保存到本地存储
      this.saveAddressesToStorage();

      // 通知父组件地址变化
      this.triggerEvent('addresschange', defaultAddress);

      wx.showToast({
        title: '默认地址设置成功',
        icon: 'success'
      });
    },

    /**
     * 阻止事件冒泡
     */
    preventBubble(e) {
      // 阻止事件冒泡，防止触发父级的点击事件
      return false;
    },

    /**
     * 阻止触摸移动事件
     */
    preventTouchMove(e) {
      // 阻止触摸移动事件，防止背景滚动
      return false;
    }
  },

  /**
   * 组件生命周期
   */
  lifetimes: {
    attached() {
      // 组件实例被放到页面节点树后执行
      this.loadUserAddress();
    }
  }
});
