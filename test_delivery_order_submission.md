# 外卖订单提交测试文档

## 修改内容总结

### 1. 主要修改
修改了 `pages/delivery/delivery.js` 中的 `performCreateDeliveryOrder()` 方法，使其能满足后端订单创建的要求。

### 2. 具体修改点

#### 2.1 添加蜂鸟相关必需字段
在订单数据中添加了以下字段：
- `service_goods_id`: 蜂鸟服务商品ID
- `base_goods_id`: 蜂鸟基础商品ID  
- `t_index_id`: 蜂鸟运力索引ID

这些字段从配送费用查询接口的响应数据 `deliveryFeeData` 中获取。

#### 2.2 修正订单金额字段
- `total_amount`: 修正为商品总金额（不包含配送费），使用 `this.data.cartTotal.price`

#### 2.3 更新响应处理逻辑
修改了订单创建成功后的响应处理，适配后端返回的标准API格式：
```javascript
if (res.code === 200 && res.data && res.data.order_no) {
  // 处理成功响应
  this.setData({
    showCheckout: false,
    orderSuccess: true,
    orderId: res.data.order_no,
    payableAmount: res.data.payable_amount,
    userBalance: res.data.user_balance,
    paymentOrderData: res.data
  });
}
```

#### 2.4 添加配送费用数据验证
在提交订单前检查是否有配送费用数据：
```javascript
if (!this.data.deliveryFeeData) {
  wx.hideLoading();
  wx.showToast({
    title: '请先查询配送费用',
    icon: 'none'
  });
  return;
}
```

## 3. 提交的订单数据格式

修改后，前端提交给后端的订单数据格式如下：

```javascript
{
  order_type: 'delivery',
  items: [
    {
      dish_id: 1,
      name: '商品名称',
      price: 10.00,
      quantity: 2
    }
  ],
  total_amount: 20.00,  // 商品总金额
  delivery_address: {
    name: '收货人',
    phone: '手机号',
    detail: '详细地址',
    latitude: 23.123456,
    longitude: 113.123456
  },
  delivery_time: {
    id: '1',
    label: '09:00-12:00',
    value: '09:00-12:00'
  },
  delivery_fee: 5.00,
  service_goods_id: 3008,
  base_goods_id: 30024,
  t_index_id: 'b69d90c2-b7f0-4f0b-a62c-4ad64aa50835',
  // 可选字段
  coupon_id: '14,17',
  coupon_discount: 2.00
}
```

## 4. 测试步骤

### 4.1 前置条件
1. 确保已选择商品到购物车
2. 确保已选择配送地址（包含经纬度坐标）
3. 确保已选择配送时间
4. 确保已查询配送费用

### 4.2 测试流程
1. 点击"去结算"按钮
2. 系统自动查询配送费用
3. 在结算页面点击"提交订单"
4. 验证订单数据是否包含所有必需字段
5. 验证后端是否成功创建订单
6. 验证前端是否正确处理响应

### 4.3 预期结果
- 订单成功创建
- 返回订单号、应付金额、用户余额等信息
- 前端显示订单成功页面
- 购物车被清空

## 5. 注意事项

1. **配送费用查询**: 必须先查询配送费用才能提交订单，因为需要获取蜂鸟相关的ID
2. **地址坐标**: 配送地址必须包含准确的经纬度坐标
3. **金额计算**: `total_amount` 是商品总金额，不包含配送费和优惠券优惠
4. **错误处理**: 如果缺少必需字段，后端会返回400错误和具体的错误信息

## 6. 地址数据结构验证

前端地址数据结构包含后端要求的所有必填字段：

```javascript
{
  id: "1234567890",
  name: "张三",           // 后端必填字段
  phone: "13800138000",   // 后端必填字段
  detail: "某某大厦1001室", // 后端必填字段
  province: "广东省",
  city: "广州市",
  district: "天河区",
  latitude: 23.123456,    // 后端验证坐标
  longitude: 113.123456,  // 后端验证坐标
  label: "家",
  isDefault: true
}
```

## 7. 错误处理

### 7.1 常见错误及解决方案

1. **缺少配送费用数据**
   - 错误：提交订单时提示"请先查询配送费用"
   - 解决：确保在点击"去结算"后等待配送费用查询完成

2. **缺少蜂鸟相关字段**
   - 错误：后端返回400错误，提示缺少必填参数
   - 解决：确保配送费用查询成功并返回了service_goods_id等字段

3. **地址坐标缺失**
   - 错误：后端可能提示地址坐标问题
   - 解决：前端会自动尝试地理编码获取坐标

4. **优惠金额不匹配**
   - 错误：后端返回优惠金额验证失败
   - 解决：重新选择优惠券或刷新页面

## 8. 兼容性说明

修改后的代码向后兼容，不会影响现有功能：
- 保持了原有的购物车逻辑
- 保持了原有的地址管理逻辑
- 保持了原有的优惠券逻辑
- 只是在订单提交时添加了必需的字段

## 9. 部署建议

1. **测试环境验证**：先在测试环境验证所有功能正常
2. **数据备份**：部署前备份相关数据
3. **分步部署**：建议先部署后端，再部署前端
4. **监控日志**：部署后密切关注订单创建相关的日志
