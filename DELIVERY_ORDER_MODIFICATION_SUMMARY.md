# 外卖订单提交修改总结

## 修改概述

修改了 `pages/delivery/delivery.js` 中的订单提交逻辑，使其能满足后端订单创建API的要求。主要是添加了蜂鸟配送服务所需的必填字段，并修正了数据格式。

## 主要修改内容

### 1. 添加蜂鸟相关必填字段

在 `performCreateDeliveryOrder()` 方法中，为订单数据添加了以下字段：

```javascript
const orderData = {
  // ... 其他字段
  service_goods_id: this.data.deliveryFeeData.service_goods_id,
  base_goods_id: this.data.deliveryFeeData.base_goods_id,
  t_index_id: this.data.deliveryFeeData.t_index_id
};
```

这些字段从配送费用查询接口 `/delivery-order/delivery-fee` 的响应中获取。

### 2. 修正订单金额字段

将 `total_amount` 修正为商品总金额（不包含配送费）：

```javascript
// 修改前
total_amount: this.data.finalAmount || this.data.cartTotal.price,

// 修改后  
total_amount: this.data.cartTotal.price, // 商品总金额，不包含配送费
```

### 3. 添加配送费用数据验证

在提交订单前检查是否有配送费用数据：

```javascript
if (!this.data.deliveryFeeData) {
  wx.hideLoading();
  wx.showToast({
    title: '请先查询配送费用',
    icon: 'none'
  });
  return;
}
```

### 4. 更新响应处理逻辑

修改了订单创建成功后的响应处理，适配后端返回的标准API格式：

```javascript
// 修改前
if (res.order_no) {

// 修改后
if (res.code === 200 && res.data && res.data.order_no) {
  this.setData({
    showCheckout: false,
    orderSuccess: true,
    orderId: res.data.order_no,
    payableAmount: res.data.payable_amount,
    userBalance: res.data.user_balance,
    paymentOrderData: res.data
  });
}
```

## 提交的订单数据格式

修改后，前端提交给后端的完整订单数据格式：

```javascript
{
  order_type: 'delivery',
  items: [
    {
      dish_id: 1,
      name: '商品名称',
      price: 10.00,
      quantity: 2
    }
  ],
  total_amount: 20.00,  // 商品总金额
  delivery_address: {
    name: '收货人',
    phone: '手机号',
    detail: '详细地址',
    latitude: 23.123456,
    longitude: 113.123456
    // ... 其他地址字段
  },
  delivery_time: {
    id: '1',
    label: '09:00-12:00',
    value: '09:00-12:00'
  },
  delivery_fee: 5.00,
  service_goods_id: 3008,      // 新增：蜂鸟服务商品ID
  base_goods_id: 30024,        // 新增：蜂鸟基础商品ID
  t_index_id: 'uuid-string',   // 新增：蜂鸟运力索引ID
  // 可选字段
  coupon_id: '14,17',          // 优惠券ID字符串
  coupon_discount: 2.00        // 预期优惠金额
}
```

## 后端API要求对比

| 字段 | 后端要求 | 前端实现 | 状态 |
|------|----------|----------|------|
| order_type | 必填，固定为"delivery" | ✓ | ✅ |
| items | 必填，商品列表 | ✓ | ✅ |
| total_amount | 必填，订单总金额 | ✓ | ✅ |
| delivery_address | 必填，包含坐标 | ✓ | ✅ |
| delivery_time | 可选，配送时间 | ✓ | ✅ |
| coupon_id | 可选，优惠券ID字符串 | ✓ | ✅ |
| coupon_discount | 可选，预期优惠金额 | ✓ | ✅ |
| delivery_fee | 必填，配送费用 | ✓ | ✅ |
| service_goods_id | 必填，蜂鸟服务商品ID | ✓ | ✅ |
| base_goods_id | 必填，蜂鸟基础商品ID | ✓ | ✅ |
| t_index_id | 必填，蜂鸟运力索引ID | ✓ | ✅ |

## 数据流程

1. **用户选择商品** → 添加到购物车
2. **用户选择地址** → 验证包含必填字段和坐标
3. **用户选择时间** → 设置配送时间
4. **点击去结算** → 调用配送费用查询接口
5. **配送费用查询** → 获取蜂鸟相关ID和配送费
6. **显示结算页面** → 用户确认订单信息
7. **提交订单** → 包含所有必填字段的完整数据
8. **后端处理** → 创建订单并返回订单信息
9. **前端处理响应** → 显示订单成功页面

## 兼容性

- ✅ 向后兼容，不影响现有功能
- ✅ 保持原有的购物车逻辑
- ✅ 保持原有的地址管理逻辑  
- ✅ 保持原有的优惠券逻辑
- ✅ 只在订单提交时添加必需字段

## 测试建议

1. **功能测试**：完整走一遍下单流程
2. **数据验证**：检查提交的订单数据格式
3. **错误处理**：测试各种异常情况
4. **兼容性测试**：确保不影响其他功能

## 注意事项

1. 必须先查询配送费用才能提交订单
2. 配送地址必须包含准确的经纬度坐标
3. 商品总金额不包含配送费和优惠券优惠
4. 蜂鸟相关字段从配送费用查询接口获取

## 部署检查清单

- [ ] 后端订单创建接口已更新
- [ ] 前端代码已修改并测试
- [ ] 配送费用查询接口正常工作
- [ ] 地址管理功能正常
- [ ] 优惠券功能正常
- [ ] 订单提交流程完整测试
- [ ] 错误处理机制验证
- [ ] 日志监控配置
