#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试蜂鸟配送配置的脚本
验证新增的配送模式配置是否正确加载
"""

import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_delivery_config():
    """测试配送配置"""
    print("蜂鸟配送配置测试")
    print("=" * 50)
    
    try:
        # 导入配置
        from app.core.config import settings
        
        print("✅ 配置加载成功")
        print(f"当前环境: {os.getenv('ENV', 'stage')}")
        print()
        
        # 测试基础配置
        print("基础配置:")
        print(f"  FENGNIAO_APP_KEY: {settings.FENGNIAO_APP_KEY[:10]}...")
        print(f"  FENGNIAO_MERCHANT_ID: {settings.FENGNIAO_MERCHANT_ID}")
        print(f"  FENGNIAO_SHOP_ID: {settings.FENGNIAO_SHOP_ID}")
        print(f"  FENGNIAO_API_URL: {settings.FENGNIAO_API_URL}")
        print()
        
        # 测试新增的配送模式配置
        print("配送模式配置:")
        print(f"  FENGNIAO_DELIVERY_MODE: {settings.FENGNIAO_DELIVERY_MODE}")
        print(f"  FENGNIAO_CHAIN_STORE_ID: {settings.FENGNIAO_CHAIN_STORE_ID}")
        print()
        
        # 测试点对点配送配置
        print("点对点配送配置:")
        print(f"  FENGNIAO_TRANSPORT_LONGITUDE: {settings.FENGNIAO_TRANSPORT_LONGITUDE}")
        print(f"  FENGNIAO_TRANSPORT_LATITUDE: {settings.FENGNIAO_TRANSPORT_LATITUDE}")
        print(f"  FENGNIAO_TRANSPORT_ADDRESS: {settings.FENGNIAO_TRANSPORT_ADDRESS}")
        print(f"  FENGNIAO_TRANSPORT_TEL: {settings.FENGNIAO_TRANSPORT_TEL}")
        print()
        
        # 验证配送模式
        delivery_mode = settings.FENGNIAO_DELIVERY_MODE.lower()
        if delivery_mode == "store":
            print("✅ 当前配置为门店发单模式")
            if settings.FENGNIAO_CHAIN_STORE_ID:
                print(f"  使用 chain_store_id: {settings.FENGNIAO_CHAIN_STORE_ID}")
            else:
                print(f"  使用 out_shop_code: {settings.FENGNIAO_SHOP_ID}")
        elif delivery_mode == "point_to_point":
            print("✅ 当前配置为点对点发单模式")
            print(f"  取货点: {settings.FENGNIAO_TRANSPORT_ADDRESS}")
            print(f"  坐标: ({settings.FENGNIAO_TRANSPORT_LONGITUDE}, {settings.FENGNIAO_TRANSPORT_LATITUDE})")
        else:
            print(f"⚠️  未知的配送模式: {delivery_mode}")
            print("  将默认使用门店发单模式")
        
        print()
        print("✅ 配置测试完成")
        
    except ImportError as e:
        print(f"❌ 导入配置失败: {e}")
        print("请确保在项目根目录运行此脚本")
    except Exception as e:
        print(f"❌ 配置测试失败: {e}")

def test_prepare_fengniao_data():
    """测试蜂鸟预下单数据准备函数"""
    print("\n" + "=" * 50)
    print("测试蜂鸟预下单数据准备")
    print("=" * 50)
    
    try:
        from app.api.v1.wechat_mini_app.delivery import _prepare_fengniao_pre_order_data
        
        # 模拟测试数据
        task_info = {
            'items': [
                {
                    'dish_id': 1,
                    'name': '素食套餐A',
                    'price': 25.00,
                    'quantity': 2
                },
                {
                    'dish_id': 2,
                    'name': '素食套餐B',
                    'price': 30.00,
                    'quantity': 1
                }
            ],
            'total_amount': 80.00
        }
        
        delivery_address = {
            'name': '张三',
            'phone': '13800138000',
            'detail': '广州市天河区珠江新城某某大厦1001室',
            'latitude': 23.120000,
            'longitude': 113.320000
        }
        
        # 调用函数
        fengniao_data = _prepare_fengniao_pre_order_data(task_info, delivery_address)
        
        print("✅ 蜂鸟预下单数据准备成功")
        print("生成的数据结构:")
        
        # 显示关键字段
        print(f"  partner_order_code: {fengniao_data.get('partner_order_code')}")
        print(f"  goods_count: {fengniao_data.get('goods_count')}")
        print(f"  goods_total_amount_cent: {fengniao_data.get('goods_total_amount_cent')}")
        print(f"  receiver_address: {fengniao_data.get('receiver_address')}")
        print(f"  position_source: {fengniao_data.get('position_source')}")
        
        # 根据模式显示不同的字段
        if 'out_shop_code' in fengniao_data:
            print(f"  out_shop_code: {fengniao_data.get('out_shop_code')} (门店发单模式)")
        if 'chain_store_id' in fengniao_data:
            print(f"  chain_store_id: {fengniao_data.get('chain_store_id')} (门店发单模式)")
        if 'transport_longitude' in fengniao_data:
            print(f"  transport_longitude: {fengniao_data.get('transport_longitude')} (点对点模式)")
            print(f"  transport_latitude: {fengniao_data.get('transport_latitude')} (点对点模式)")
            print(f"  transport_address: {fengniao_data.get('transport_address')} (点对点模式)")
        
        print("\n✅ 数据准备测试完成")
        
    except ImportError as e:
        print(f"❌ 导入函数失败: {e}")
    except Exception as e:
        print(f"❌ 数据准备测试失败: {e}")

if __name__ == "__main__":
    test_delivery_config()
    test_prepare_fengniao_data()
