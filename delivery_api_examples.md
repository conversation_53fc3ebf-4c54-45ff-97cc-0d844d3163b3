# 外卖配送价格API返回示例

## API接口信息

- **接口路径**: `/api/v1/wechat_mini_app/delivery-order/create`
- **请求方法**: POST
- **功能**: 获取外卖配送价格（预下单接口）
- **支持模式**: 门店发单、点对点发单（可配置切换）

## 配置说明

### 配送模式配置

在配置文件中可以设置以下参数来控制配送模式：

```bash
# 配送模式: store(门店发单) 或 point_to_point(点对点发单)
FENGNIAO_DELIVERY_MODE=store

# 门店发单模式参数
FENGNIAO_SHOP_ID=461841708                    # 外部门店ID (out_shop_code)
FENGNIAO_CHAIN_STORE_ID=                      # 门店ID (chain_store_id) - 可选

# 点对点发单模式参数
FENGNIAO_TRANSPORT_LONGITUDE=113.431943      # 取货点经度
FENGNIAO_TRANSPORT_LATITUDE=23.095987        # 取货点纬度
FENGNIAO_TRANSPORT_ADDRESS=取货点地址描述      # 取货点地址
FENGNIAO_TRANSPORT_TEL=13800138000           # 取货点联系电话
```

## 请求示例

```json
{
  "order_type": "delivery",
  "items": [
    {
      "dish_id": 1,
      "name": "素食套餐A",
      "price": 25.00,
      "quantity": 2
    },
    {
      "dish_id": 2,
      "name": "素食套餐B",
      "price": 30.00,
      "quantity": 1
    }
  ],
  "total_amount": 80.00,
  "delivery_address": {
    "name": "张三",
    "phone": "13800138000",
    "detail": "广州市天河区珠江新城某某大厦1001室",
    "province": "广东省",
    "city": "广州市",
    "district": "天河区",
    "latitude": 23.120000,
    "longitude": 113.320000
  },
  "delivery_time": {
    "type": "immediate",
    "value": ""
  }
}
```

## 成功响应示例

### 门店发单模式响应

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "delivery_fee": 11.70,
    "delivery_fee_cent": 1170,
    "predict_delivery_minutes": 64,
    "distance": 4447,
    "service_goods_id": 3008,
    "base_goods_id": 30024,
    "slogan": "24小时服务，灵活配送准点达",
    "t_index_id": "b69d90c2-b7f0-4f0b-a62c-4ad64aa50835"
  }
}
```

### 点对点发单模式响应

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "delivery_fee": 15.50,
    "delivery_fee_cent": 1550,
    "predict_delivery_minutes": 45,
    "distance": 3200,
    "service_goods_id": 3010,
    "base_goods_id": 30026,
    "slogan": "点对点专送，快速直达",
    "t_index_id": "c79e91d3-c8g1-5g1c-b73d-5be75bb61946"
  }
}
```

## 错误响应示例

### 参数错误

```json
{
  "code": 400,
  "message": "缺少必填参数: delivery_address",
  "data": null
}
```

### 用户未登录

```json
{
  "code": 401,
  "message": "未登录",
  "data": null
}
```

### 配送服务不可用

```json
{
  "code": 500,
  "message": "获取配送价格失败: 当前区域无运力",
  "data": null
}
```

### 蜂鸟API错误

```json
{
  "code": 500,
  "message": "获取配送价格失败: 运单运力紧张，请稍后重试",
  "data": null
}
```

## 响应字段说明

| 字段名 | 类型 | 说明 |
|--------|------|------|
| code | Integer | 响应状态码，200表示成功 |
| message | String | 响应消息 |
| data | Object | 响应数据，失败时为null |
| data.delivery_fee | Float | 配送费用（元） |
| data.delivery_fee_cent | Integer | 配送费用（分） |
| data.predict_delivery_minutes | Integer | 预计送达时间（分钟） |
| data.distance | Integer | 配送距离（米） |
| data.service_goods_id | Integer | 服务商品ID |
| data.base_goods_id | Integer | 基础商品ID |
| data.slogan | String | 服务介绍 |
| data.t_index_id | String | 预询标识，正式下单时需要传入 |

## 注意事项

1. **配送模式限制**: 根据蜂鸟文档，预下单接口仅支持门店发单模式
2. **坐标要求**: delivery_address中必须包含准确的latitude和longitude坐标
3. **价格一致性**: 正式下单时需要校验价格与预下单结果一致
4. **预询标识**: 正式下单时需要传入t_index_id参数
5. **配置切换**: 可通过修改FENGNIAO_DELIVERY_MODE配置在两种模式间切换

## 测试方法

使用项目根目录下的测试脚本：

```bash
# 测试配置
python test_delivery_config.py

# 测试API（支持两种模式）
python test_delivery_api.py
```
